import {
  ChangeDetectionStrategy,
  Component,
  Input,
  Output,
  EventEmitter,
  ChangeDetectorRef,
  Inject,
  PLATFORM_ID,
  signal,
  computed,
} from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { OptimizedBaseComponent } from '@components/common/base/optimized-base.component';
import { BreadcrumbComponent } from '@components/common/breadcrumb/breadcrumb.component';
import { AnouncementComponent } from '@components/common/anouncement/anouncement.component';
import { Comic, ChapterPage } from '@schema';

@Component({
  selector: 'app-chapter-header',
  standalone: true,
  imports: [CommonModule, RouterModule, BreadcrumbComponent, AnouncementComponent],
  changeDetection: ChangeDetectionStrategy.OnPush,
  template: `
    <div class="chapter-header-container">
      <div class="header-container">
        <app-breadcrumb
          class="breadcrumb-wrapper"
          [Links]="breadcrumbLinks()"
        >
        </app-breadcrumb>
        <app-anouncement></app-anouncement>
      </div>

      <div class="chapter-header-card">
        <!-- Chapter Info -->
        <div class="chapter-info-section">
          <!-- Comic Title -->
          <div class="comic-title-section">
            <h1 class="comic-title">
              <a
                [routerLink]="comicRouterLink()"
                [title]="comic()?.title || ''"
                class="comic-title-link"
              >
                {{ comic()?.title }}
              </a>
            </h1>
          </div>

          <!-- Chapter Details -->
          <div class="chapter-details">
            <h2 class="chapter-title">{{ mainChapter()?.title }}</h2>
            <time class="chapter-date" [dateTime]="mainChapter()?.updateAt | date : 'yyyy-MM-dd'">
              Đăng lúc: {{ mainChapter()?.updateAt | date : 'dd/MM/yyyy' }}
            </time>
          </div>
        </div>

        <!-- Server Selection -->
        <div class="server-selection-section">
          <div class="server-list">
            <button
              *ngFor="
                let serverId of displayedServerIds();
                let i = index;
                trackBy: trackByServerId
              "
              (click)="onServerChange(serverId, i)"
              class="server-button"
              [class.server-button-active]="i === selectedServerId()"
            >
              <svg class="server-icon" viewBox="0 0 24 24">
                <path d="M7 18a4.6 4.4 0 0 1 0 -9h0a5 4.5 0 0 1 11 2h1a3.5 3.5 0 0 1 0 7h-12" />
              </svg>
              <span class="server-text">Server {{ i + 1 }}</span>
            </button>

            <button
              (click)="onToggleServers()"
              *ngIf="hasMoreServers()"
              class="server-expand-button"
            >
              <svg
                class="expand-icon"
                [class.expand-icon-rotated]="showAllServers()"
                viewBox="0 0 24 24"
              >
                <path d="M18 15l-6-6l-6 6h12" />
              </svg>
            </button>
            
            <button class="report-error-button" (click)="onReportError()">
              <svg class="report-icon" viewBox="0 0 24 24">
                <path
                  d="M10.29 3.86L1.82 18a2 2 0 0 0 1.71 3h16.94a2 2 0 0 0 1.71-3L13.71 3.86a2 2 0 0 0-3.42 0z"
                />
                <line x1="12" y1="9" x2="12" y2="13" />
                <line x1="12" y1="17" x2="12.01" y2="17" />
              </svg>
              <span class="report-text">Báo lỗi</span>
            </button>
          </div>
        </div>
      </div>
    </div>
  `,
  styleUrl: './chapter-header.component.scss'
})
export class ChapterHeaderComponent extends OptimizedBaseComponent {
  @Input()
  set comic(value: Comic | null) {
    this.comicSignal.set(value);
  }
  get comic() {
    return this.comicSignal.asReadonly();
  }

  @Input()
  set mainChapter(value: ChapterPage | null) {
    this.mainChapterSignal.set(value);
  }
  get mainChapter() {
    return this.mainChapterSignal.asReadonly();
  }

  @Input()
  set listChapterServerIds(value: number[]) {
    this.listChapterServerIdsSignal.set(value);
  }
  get listChapterServerIds() {
    return this.listChapterServerIdsSignal.asReadonly();
  }

  @Input()
  set selectedServerId(value: number) {
    this.selectedServerIdSignal.set(value);
  }
  get selectedServerId() {
    return this.selectedServerIdSignal.asReadonly();
  }

  @Input()
  set showAllServers(value: boolean) {
    this.showAllServersSignal.set(value);
  }
  get showAllServers() {
    return this.showAllServersSignal.asReadonly();
  }

  @Output() serverChange = new EventEmitter<{ serverId: number; index: number }>();
  @Output() toggleServers = new EventEmitter<void>();
  @Output() reportError = new EventEmitter<void>();

  // Signals for reactive state
  private readonly comicSignal = signal<Comic | null>(null);
  private readonly mainChapterSignal = signal<ChapterPage | null>(null);
  private readonly listChapterServerIdsSignal = signal<number[]>([]);
  private readonly selectedServerIdSignal = signal<number>(0);
  private readonly showAllServersSignal = signal<boolean>(false);

  // Computed properties for optimized access
  readonly breadcrumbLinks = computed(() => {
    const comic = this.comic();
    const mainChapter = this.mainChapter();
    
    return [
      { label: 'Trang chủ', url: '/' },
      {
        label: comic?.title || '',
        url: `/truyen-tranh/${comic?.url || ''}-${comic?.id || ''}`
      },
      { label: mainChapter?.title || '', url: '' }
    ];
  });

  readonly comicRouterLink = computed(() => {
    const comic = this.comic();
    return comic ? ['/truyen-tranh', `${comic.url}-${comic.id}`] : [];
  });

  readonly displayedServerIds = computed(() => {
    const serverIds = this.listChapterServerIds();
    const showAll = this.showAllServers();
    return showAll ? serverIds : serverIds.slice(0, 3);
  });

  readonly hasMoreServers = computed(() => {
    return this.listChapterServerIds().length > 3;
  });

  constructor(
    cdr: ChangeDetectorRef,
    @Inject(PLATFORM_ID) platformId: object
  ) {
    super(cdr, platformId);
  }

  // TrackBy functions for performance
  trackByServerId = (index: number, serverId: number): number => serverId;

  // Event handlers
  onServerChange(serverId: number, index: number): void {
    this.serverChange.emit({ serverId, index });
  }

  onToggleServers(): void {
    this.toggleServers.emit();
  }

  onReportError(): void {
    this.reportError.emit();
  }
}
