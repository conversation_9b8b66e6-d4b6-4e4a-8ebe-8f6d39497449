// ===== CHAPTER CONTROL BAR COMPONENT STYLES =====
// Modern, clean design with Tailwind CSS @apply directives

// ===== CONTROL BAR CONTAINER =====
.control-bar-container {
  @apply w-full h-12;
}

// ===== CONTROL BAR =====
.control-bar {
  @apply flex justify-center max-w-full items-center z-10 rounded-b-lg py-1.5 px-2 gap-2 md:gap-3;
  @apply bg-white dark:bg-neutral-800 dark:border-neutral-700;
  @apply transition-[top] duration-500 ease-in-out z-[999];
  @apply transform-gpu; // Enable hardware acceleration

  &.sticky-top {
    @apply fixed top-0 border;
    &.modern {
      @apply top-2;
    }
  }
  
  &.sticky-invisible {
    @apply fixed -top-12;
  }

  &.sticky-top,
  &.sticky-invisible {
    &.modern {
      @apply left-1/2 -translate-x-1/2 rounded-xl;
    }
    &.classic {
      @apply left-0 right-0 rounded-none;
    }
  }
}

.control-group {
  @apply z-10 flex items-center gap-2;
}

.control-button {
  @apply flex items-center gap-2 px-3 py-2 text-sm font-medium text-gray-600 dark:text-gray-300 hover:text-primary-100 hover:bg-gray-100 dark:hover:bg-neutral-700 rounded-lg border border-gray-200 dark:border-neutral-600 hover:border-primary-100/50 transition-all duration-200;

  &.control-button-home {
    @apply text-primary-100 border-primary-100/30 bg-primary-100/5;
  }

  &.settings-button {
    @apply bg-gray-50 dark:bg-neutral-700;
  }
}

.control-icon {
  @apply w-4 h-4;
  fill: none;
  stroke: currentColor;
  stroke-width: 2;
  stroke-linecap: round;
  stroke-linejoin: round;
}

// ===== CHAPTER NAVIGATION =====
.chapter-navigation-group {
  @apply flex items-center gap-1 px-4;
}

.nav-button {
  @apply flex items-center gap-2 px-2 py-2 text-sm font-medium text-gray-600 dark:text-gray-300 hover:text-white bg-gray-200 dark:bg-neutral-600 hover:bg-primary-100 rounded-lg border-none cursor-pointer disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200;

  &.nav-button-active {
    @apply text-white bg-primary-100;
  }
}

.nav-icon {
  @apply w-5 h-5;
  fill: none;
  stroke: currentColor;
  stroke-width: 2;
  stroke-linecap: round;
  stroke-linejoin: round;
}

.chapter-selector-wrapper {
  @apply mx-1;
}

// ===== ZOOM CONTROLS =====
.zoom-group {
  @apply relative;
}

.zoom-button {
  @apply relative;
}

.zoom-panel {
  @apply absolute hidden bg-white dark:bg-neutral-800 rounded-lg shadow-lg border border-gray-200 dark:border-neutral-700 top-12 p-4 space-y-3 items-center -translate-x-full min-w-40 z-[9999];

  &.zoom-panel-active {
    @apply block;
  }
}

.zoom-info {
  @apply flex items-center gap-4 w-full;
}

.zoom-percentage {
  @apply text-primary-100 text-sm font-semibold min-w-12;
}

.zoom-controls {
  @apply flex gap-2;
}

.zoom-control-btn {
  @apply flex items-center justify-center w-8 h-8 text-gray-600 dark:text-gray-300 hover:text-primary-100 hover:bg-gray-100 dark:hover:bg-neutral-700 rounded-md transition-all duration-200;
}

.zoom-control-icon {
  @apply w-4 h-4;
  fill: none;
  stroke: currentColor;
  stroke-width: 2;
  stroke-linecap: round;
  stroke-linejoin: round;
}

.zoom-reset-btn {
  @apply flex items-center gap-2 px-3 py-1.5 bg-primary-100 hover:bg-primary-200 text-white text-sm font-medium rounded-lg border-none cursor-pointer transition-all duration-200;
}

.zoom-reset-icon {
  @apply w-4 h-4;
  fill: none;
  stroke: currentColor;
  stroke-width: 2;
  stroke-linecap: round;
  stroke-linejoin: round;
}

// ===== RESPONSIVE DESIGN =====
@media (max-width: 768px) {
  .chapter-navigation-group {
    @apply px-1 gap-0;
  }

  .nav-button {
    @apply px-1 py-1.5 text-xs;
  }
}

@media (max-width: 640px) {
  .control-bar {
    @apply gap-1 px-1;
  }

  .control-button {
    @apply px-2 py-1.5 text-xs;
  }

  .zoom-panel {
    @apply min-w-32 p-3;
  }
}
