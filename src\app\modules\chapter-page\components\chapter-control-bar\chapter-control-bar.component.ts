import {
  ChangeDetectionStrategy,
  Component,
  Input,
  Output,
  EventEmitter,
  ChangeDetectorRef,
  Inject,
  PLATFORM_ID,
  signal,
  computed,
  ViewChild,
  ElementRef,
} from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { OptimizedBaseComponent } from '@components/common/base/optimized-base.component';
import { ChapterSelectorComponent } from '@components/common/chapter-selector/chapter-selector.component';
import { ClickOutsideDirective } from '@directives/click-outside.directive';
import { Chapter, ChapterPage, Comic } from '@schema';

export interface ZoomData {
  minZoomLevel: number;
  maxZoomLevel: number;
  zoomValue: number;
  defaultZoomLevel: number;
  isZoomIn: boolean;
}

export interface ChapterSettings {
  toolbarStyle: string;
}

@Component({
  selector: 'app-chapter-control-bar',
  standalone: true,
  imports: [CommonModule, RouterModule, ChapterSelectorComponent, ClickOutsideDirective],
  changeDetection: ChangeDetectionStrategy.OnPush,
  template: `
    <nav #controlBarContainer class="control-bar-container">
      <div #controlBar class="control-bar" [ngClass]="chapterSettings()?.toolbarStyle">
        <!-- Home Button -->
        <div class="control-group">
          <a href="" title="Trang chủ" class="control-button control-button-home">
            <svg class="control-icon" viewBox="0 0 24 24">
              <path d="M3 9l9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z" />
              <polyline points="9,22 9,12 15,12 15,22" />
            </svg>
          </a>
        </div>

        <!-- Fullscreen Button -->
        <div class="control-group">
          <button title="Toàn màn hình" class="control-button" (click)="onToggleFullscreen()">
            <svg class="control-icon" viewBox="0 0 24 24">
              <path
                d="M8 3H5a2 2 0 0 0-2 2v3m18 0V5a2 2 0 0 0-2-2h-3m0 18h3a2 2 0 0 0 2-2v-3M3 16v3a2 2 0 0 0 2 2h3"
              />
            </svg>
          </button>
        </div>

        <!-- Chapter Navigation -->
        <div class="chapter-navigation-group">
          <button
            class="nav-button nav-button-prev"
            (click)="onNavigateChapter(true)"
            aria-label="Chương trước"
            [disabled]="isImageLoading()"
            [class.nav-button-active]="canNavigatePrev()"
          >
            <svg class="nav-icon" viewBox="0 0 24 24">
              <polyline points="15 18 9 12 15 6" />
            </svg>
          </button>

          <div class="chapter-selector-wrapper">
            <app-chapter-selector
              [chapters]="comic()?.chapters || []"
              [mainChapter]="mainChapter()"
              (chapterChange)="onChapterChange($event)"
              [topToBottom]="true"
            >
            </app-chapter-selector>
          </div>

          <button
            class="nav-button nav-button-next"
            (click)="onNavigateChapter(false)"
            aria-label="Chương tiếp"
            [disabled]="isImageLoading()"
            [class.nav-button-active]="canNavigateNext()"
          >
            <svg class="nav-icon" viewBox="0 0 24 24">
              <polyline points="9 18 15 12 9 6" />
            </svg>
          </button>
        </div>

        <!-- Zoom Controls -->
        <div
          class="control-group zoom-group"
          (appClickOutside)="closeZoomPanel()"
        >
          <button
            title="Thu phóng"
            class="control-button zoom-button"
            (click)="onZoomToggle()"
          >
            <svg class="control-icon" viewBox="0 0 24 24">
              @if (!zoomData()?.isZoomIn) {
              <circle cx="11" cy="11" r="8" />
              <line x1="21" y1="21" x2="16.65" y2="16.65" />
              <line x1="11" y1="8" x2="11" y2="14" />
              <line x1="8" y1="11" x2="14" y2="11" />
              } @else {
              <circle cx="11" cy="11" r="8" />
              <line x1="21" y1="21" x2="16.65" y2="16.65" />
              <line x1="8" y1="11" x2="14" y2="11" />
              }
            </svg>
          </button>
          <div #zoomPanel class="zoom-panel" [class.zoom-panel-active]="showZoomPanel()">
            <div class="zoom-info">
              <span class="zoom-percentage">{{ zoomPercentage() }}%</span>
              <div class="zoom-controls">
                <button class="zoom-control-btn" (click)="onZoomOut()" title="Thu nhỏ">
                  <svg class="zoom-control-icon" viewBox="0 0 24 24">
                    <line x1="5" y1="12" x2="19" y2="12" />
                  </svg>
                </button>
                <button class="zoom-control-btn" (click)="onZoomIn()" title="Phóng to">
                  <svg class="zoom-control-icon" viewBox="0 0 24 24">
                    <line x1="12" y1="5" x2="12" y2="19" />
                    <line x1="5" y1="12" x2="19" y2="12" />
                  </svg>
                </button>
              </div>
            </div>

            <button (click)="onZoomReset()" title="Đặt lại" class="zoom-reset-btn">
              <svg class="zoom-reset-icon" viewBox="0 0 24 24">
                <path
                  d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
                />
              </svg>
            </button>
          </div>
        </div>

        <!-- Settings Button -->
        <div class="control-group">
          <button (click)="onOpenSettings()" title="Cài đặt" class="control-button settings-button">
            <svg
              class="control-icon"
              width="24"
              height="24"
              viewBox="0 0 24 24"
              stroke-width="2"
              stroke="currentColor"
              fill="none"
              stroke-linecap="round"
              stroke-linejoin="round"
            >
              <path stroke="none" d="M0 0h24v24H0z" />
              <path
                d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 0 0 2.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 0 0 1.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 0 0 -1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 0 0 -2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 0 0 -2.573 -1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 0 0 -1.065 -2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 0 0 1.066 -2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"
              />
              <circle cx="12" cy="12" r="3" />
            </svg>
          </button>
        </div>
      </div>
    </nav>
  `,
  styleUrl: './chapter-control-bar.component.scss'
})
export class ChapterControlBarComponent extends OptimizedBaseComponent {
  @ViewChild('controlBar') controlBar!: ElementRef;
  @ViewChild('controlBarContainer') controlBarContainer!: ElementRef;
  @ViewChild('zoomPanel') zoomPanel!: ElementRef;

  @Input()
  set comic(value: Comic | null) {
    this.comicSignal.set(value);
  }
  get comic() {
    return this.comicSignal.asReadonly();
  }

  @Input()
  set mainChapter(value: ChapterPage | null) {
    this.mainChapterSignal.set(value);
  }
  get mainChapter() {
    return this.mainChapterSignal.asReadonly();
  }

  @Input()
  set isImageLoading(value: boolean) {
    this.isImageLoadingSignal.set(value);
  }
  get isImageLoading() {
    return this.isImageLoadingSignal.asReadonly();
  }

  @Input()
  set zoomData(value: ZoomData | null) {
    this.zoomDataSignal.set(value);
  }
  get zoomData() {
    return this.zoomDataSignal.asReadonly();
  }

  @Input()
  set chapterSettings(value: ChapterSettings | null) {
    this.chapterSettingsSignal.set(value);
  }
  get chapterSettings() {
    return this.chapterSettingsSignal.asReadonly();
  }

  @Output() toggleFullscreen = new EventEmitter<void>();
  @Output() navigateChapter = new EventEmitter<boolean>();
  @Output() chapterChange = new EventEmitter<number>();
  @Output() zoomIn = new EventEmitter<void>();
  @Output() zoomOut = new EventEmitter<void>();
  @Output() zoomReset = new EventEmitter<void>();
  @Output() openSettings = new EventEmitter<void>();

  // Signals for reactive state
  private readonly comicSignal = signal<Comic | null>(null);
  private readonly mainChapterSignal = signal<ChapterPage | null>(null);
  private readonly isImageLoadingSignal = signal<boolean>(false);
  private readonly zoomDataSignal = signal<ZoomData | null>(null);
  private readonly chapterSettingsSignal = signal<ChapterSettings | null>(null);
  private readonly showZoomPanelSignal = signal<boolean>(false);

  // Computed properties for optimized access
  readonly canNavigatePrev = computed(() => {
    const comic = this.comic();
    const mainChapter = this.mainChapter();
    
    if (!comic?.chapters || !mainChapter) return false;
    
    return comic.chapters.length > 0 &&
           mainChapter.slug !== comic.chapters[comic.chapters.length - 1]?.slug;
  });

  readonly canNavigateNext = computed(() => {
    const comic = this.comic();
    const mainChapter = this.mainChapter();
    
    if (!comic?.chapters || !mainChapter) return false;
    
    return comic.chapters.length > 0 && mainChapter.slug !== comic.chapters[0]?.slug;
  });

  readonly zoomPercentage = computed(() => {
    const zoomData = this.zoomData();
    return Math.round(zoomData?.zoomValue || 100);
  });

  readonly showZoomPanel = computed(() => this.showZoomPanelSignal());

  constructor(
    cdr: ChangeDetectorRef,
    @Inject(PLATFORM_ID) platformId: object
  ) {
    super(cdr, platformId);
  }

  // Event handlers
  onToggleFullscreen(): void {
    this.toggleFullscreen.emit();
  }

  onNavigateChapter(isNext: boolean): void {
    this.navigateChapter.emit(isNext);
  }

  onChapterChange(chapterId: number): void {
    this.chapterChange.emit(chapterId);
  }

  onZoomToggle(): void {
    const zoomData = this.zoomData();
    if (zoomData) {
      if (zoomData.isZoomIn) {
        this.zoomOut.emit();
      } else {
        this.zoomIn.emit();
      }
      this.showZoomPanelSignal.set(true);
    }
  }

  onZoomIn(): void {
    this.zoomIn.emit();
  }

  onZoomOut(): void {
    this.zoomOut.emit();
  }

  onZoomReset(): void {
    this.zoomReset.emit();
  }

  onOpenSettings(): void {
    this.openSettings.emit();
  }

  closeZoomPanel(): void {
    this.showZoomPanelSignal.set(false);
  }

  // Public methods for parent component access
  getControlBar(): ElementRef {
    return this.controlBar;
  }

  getControlBarContainer(): ElementRef {
    return this.controlBarContainer;
  }
}
