import {
  ChangeDetectionStrategy,
  Component,
  Input,
  Output,
  EventEmitter,
  ChangeDetectorRef,
  Inject,
  PLATFORM_ID,
  signal,
  computed,
  ViewChild,
  ElementRef,
  OnInit,
  OnDestroy,
} from '@angular/core';
import { CommonModule } from '@angular/common';
import { OptimizedBaseComponent } from '@components/common/base/optimized-base.component';
import { OptimizedImageComponent } from '@components/common/optimized-image/optimized-image.component';
import { Page } from '@schema';

export interface ChapterSettings {
  isVertical: boolean;
  isNightMode: boolean;
  isFullScreen: boolean;
  preloadPages: number;
}

export interface ZoomData {
  zoomValue: number;
}

@Component({
  selector: 'app-chapter-image-viewer',
  standalone: true,
  imports: [CommonModule, OptimizedImageComponent],
  changeDetection: ChangeDetectionStrategy.OnPush,
  template: `
    <div (dblclick)="onDoubleClick()" class="reading-container" #readingContainer>
      <div
        #imageContainer
        [style.width]="imageWidth()"
        [style.left]="imageLeft()"
        id="image-container"
        class="reading-content"
      >
        <!-- Scroll Navigation -->
        <div
          class="scroll-navigation"
          [class.scroll-navigation-hidden]="hideScrollNavigation()"
        >
          <button
            (click)="onScrollHorizontal(-1)"
            class="scroll-btn scroll-btn-prev"
            title="Trang trước"
          >
            <svg class="scroll-btn-icon" viewBox="0 0 24 24">
              <polyline points="15 18 9 12 15 6" />
            </svg>
            <span class="scroll-btn-text">Trước</span>
          </button>

          <button
            (click)="onScrollHorizontal(1)"
            class="scroll-btn scroll-btn-next"
            title="Trang tiếp"
          >
            <span class="scroll-btn-text">Tiếp</span>
            <svg class="scroll-btn-icon" viewBox="0 0 24 24">
              <polyline points="9 18 15 12 9 6" />
            </svg>
          </button>
        </div>

        <!-- Loading State -->
        <div *ngIf="isImageLoading()" class="loading-container">
          <div class="loading-content">
            <div class="loading-spinner">
              <svg class="loading-icon" viewBox="0 0 24 24">
                <circle class="loading-circle-bg" cx="12" cy="12" r="10" />
                <circle class="loading-circle-progress" cx="12" cy="12" r="10" />
              </svg>
            </div>
            <div class="loading-text">
              <h3 class="loading-title">Đang tải chương...</h3>
              <p class="loading-subtitle">Vui lòng đợi trong giây lát</p>
            </div>
          </div>
        </div>

        <!-- Chapter Images with Virtual Scrolling -->
        <ng-container *ngIf="isBrowser && !isImageLoading()">
          <div class="chapter-images-container">
            <div
              *ngFor="let page of listImgs; let i = index; trackBy: trackByPageId"
              class="chapter-page-container"
              [attr.data-page-index]="i"
            >
              <app-optimized-image
                [src]="page.url"
                [alt]="getImageAlt(i)"
                [loading]="getLoadingStrategy(i)"
                [imageClass]="getImageClasses()"
                [containerClass]="'chapter-page-wrapper'"
                [preload]="shouldPreload(i)"
              />

              <!-- Skeleton placeholder -->
              <div
                *ngIf="showSkeleton(i)"
                class="skeleton"
                style="aspect-ratio: 2/3"
              ></div>
            </div>
          </div>
        </ng-container>
      </div>
    </div>
  `,
  styleUrl: './chapter-image-viewer.component.scss'
})
export class ChapterImageViewerComponent extends OptimizedBaseComponent implements OnInit, OnDestroy {
  @ViewChild('imageContainer') imageContainer!: ElementRef;
  @ViewChild('readingContainer') readingContainer!: ElementRef;

  @Input()
  set listImgs(value: Page[]) {
    this.listImgsSignal.set(value);
  }
  get listImgs() {
    return this.listImgsSignal.asReadonly();
  }

  @Input()
  set isImageLoading(value: boolean) {
    this.isImageLoadingSignal.set(value);
  }
  get isImageLoading() {
    return this.isImageLoadingSignal.asReadonly();
  }

  @Input()
  set chapterSettings(value: ChapterSettings | null) {
    this.chapterSettingsSignal.set(value);
  }
  get chapterSettings() {
    return this.chapterSettingsSignal.asReadonly();
  }

  @Input()
  set zoomData(value: ZoomData | null) {
    this.zoomDataSignal.set(value);
  }
  get zoomData() {
    return this.zoomDataSignal.asReadonly();
  }

  @Input()
  set comicUrl(value: string) {
    this.comicUrlSignal.set(value);
  }
  get comicUrl() {
    return this.comicUrlSignal.asReadonly();
  }

  @Output() doubleClick = new EventEmitter<void>();
  @Output() scrollHorizontal = new EventEmitter<number>();
  @Output() imageLoad = new EventEmitter<{ event: Event; index: number }>();
  @Output() imageError = new EventEmitter<{ event: Event; index: number }>();

  // Signals for reactive state
  private readonly listImgsSignal = signal<Page[]>([]);
  private readonly isImageLoadingSignal = signal<boolean>(false);
  private readonly chapterSettingsSignal = signal<ChapterSettings | null>(null);
  private readonly zoomDataSignal = signal<ZoomData | null>(null);
  private readonly comicUrlSignal = signal<string>('');
  private readonly loadedImagesSignal = signal<Set<number>>(new Set());
  private readonly errorImagesSignal = signal<Set<number>>(new Set());

  // Virtual scrolling state
  private readonly visibleRangeSignal = signal<{ start: number; end: number }>({ start: 0, end: 10 });
  private intersectionObserver?: IntersectionObserver;

  // Computed properties for optimized access
  readonly imageWidth = computed(() => {
    const zoomData = this.zoomData();
    return zoomData ? `${zoomData.zoomValue}%` : '100%';
  });

  readonly imageLeft = computed(() => {
    const zoomData = this.zoomData();
    return zoomData ? `${(100 - zoomData.zoomValue) * 0.5}%` : '0%';
  });

  readonly hideScrollNavigation = computed(() => {
    const settings = this.chapterSettings();
    return settings ? (settings.isVertical || settings.isFullScreen) : true;
  });

  readonly visiblePages = computed(() => {
    const pages = this.listImgs();
    const range = this.visibleRangeSignal();
    return pages.slice(range.start, range.end);
  });

  readonly useOptimizedImages = computed(() => {
    // Use optimized images for better performance
    return true;
  });

  constructor(
    cdr: ChangeDetectorRef,
    @Inject(PLATFORM_ID) platformId: object
  ) {
    super(cdr, platformId);
  }

  ngOnInit(): void {
    this.runInBrowser(() => {
      this.setupVirtualScrolling();
    });
  }

  override ngOnDestroy(): void {
    super.ngOnDestroy();
    if (this.intersectionObserver) {
      this.intersectionObserver.disconnect();
    }
  }

  // Virtual scrolling setup
  private setupVirtualScrolling(): void {
    if (!this.isBrowser) return;

    this.intersectionObserver = new IntersectionObserver(
      (entries) => {
        entries.forEach(entry => {
          if (entry.isIntersecting) {
            const pageIndex = parseInt(entry.target.getAttribute('data-page-index') || '0');
            this.updateVisibleRange(pageIndex);
          }
        });
      },
      {
        rootMargin: '100px 0px', // Load images 100px before they come into view
        threshold: 0.1
      }
    );
  }

  private updateVisibleRange(centerIndex: number): void {
    const totalPages = this.listImgs().length;
    const bufferSize = 5; // Number of pages to load before and after visible area
    
    const start = Math.max(0, centerIndex - bufferSize);
    const end = Math.min(totalPages, centerIndex + bufferSize + 1);
    
    this.visibleRangeSignal.set({ start, end });
  }

  // TrackBy functions for performance
  trackByPageId = (index: number, page: Page): string => {
    return page?.url ?? `page-${index}`;
  };

  // Helper methods
  getPageIndex(page: Page): number {
    return this.listImgs().indexOf(page);
  }

  getImageAlt(index: number): string {
    const comicUrl = this.comicUrl();
    return `${comicUrl}-chapter-page-${index + 1}`;
  }

  getLoadingStrategy(index: number): 'eager' | 'lazy' {
    const settings = this.chapterSettings();
    const preloadPages = settings?.preloadPages || 3;
    return index <= preloadPages ? 'eager' : 'lazy';
  }

  getImageClasses(): string {
    const settings = this.chapterSettings();
    if (!settings) return 'chapter-page-image';
    
    const classes = ['chapter-page-image'];
    if (!settings.isVertical) classes.push('chapter-page-horizontal');
    if (settings.isNightMode) classes.push('night-mode');
    
    return classes.join(' ');
  }

  shouldPreload(index: number): boolean {
    const settings = this.chapterSettings();
    const preloadPages = settings?.preloadPages || 3;
    return index <= preloadPages;
  }

  showSkeleton(index: number): boolean {
    const loadedImages = this.loadedImagesSignal();
    const errorImages = this.errorImagesSignal();
    return !loadedImages.has(index) && !errorImages.has(index);
  }

  // Event handlers
  onDoubleClick(): void {
    this.doubleClick.emit();
  }

  onScrollHorizontal(direction: number): void {
    this.scrollHorizontal.emit(direction);
  }

  onImageLoad(event: Event, index: number): void {
    const loadedImages = new Set(this.loadedImagesSignal());
    loadedImages.add(index);
    this.loadedImagesSignal.set(loadedImages);
    this.imageLoad.emit({ event, index });
  }

  onImageError(event: Event, index: number): void {
    const errorImages = new Set(this.errorImagesSignal());
    errorImages.add(index);
    this.errorImagesSignal.set(errorImages);
    this.imageError.emit({ event, index });
  }

  // Public methods for parent component access
  getImageContainer(): ElementRef {
    return this.imageContainer;
  }

  getReadingContainer(): ElementRef {
    return this.readingContainer;
  }
}
