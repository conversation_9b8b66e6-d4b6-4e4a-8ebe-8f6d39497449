// ===== CHAPTER IMAGE VIEWER COMPONENT STYLES =====
// Modern, clean design with Tailwind CSS @apply directives

// ===== READING CONTAINER =====
.reading-container {
  @apply sm:px-[5%] md:px-[15%] relative z-0;
}

.reading-content {
  @apply relative min-h-screen;
}

// ===== SCROLL NAVIGATION =====
.scroll-navigation {
  @apply absolute flex justify-between w-full z-20 h-1/4 top-1/3 px-4;

  &.scroll-navigation-hidden {
    @apply hidden;
  }
}

.scroll-btn {
  @apply flex items-center gap-2 px-4 py-3 bg-black/20 hover:bg-black/40 text-white rounded-lg backdrop-blur-sm border border-white/20 transition-all duration-200;

  &.scroll-btn-prev {
    @apply bg-gradient-to-r from-black/30 to-transparent;
  }

  &.scroll-btn-next {
    @apply bg-gradient-to-l from-black/30 to-transparent;
  }
}

.scroll-btn-icon {
  @apply w-6 h-6;
  fill: none;
  stroke: currentColor;
  stroke-width: 2;
  stroke-linecap: round;
  stroke-linejoin: round;
}

.scroll-btn-text {
  @apply font-medium hidden sm:inline;
}

// ===== LOADING STATE =====
.loading-container {
  @apply flex flex-col items-center justify-center mt-10 space-y-8 p-8;
}

.loading-content {
  @apply flex flex-col items-center space-y-4;
}

.loading-spinner {
  @apply relative;
}

.loading-icon {
  @apply w-16 h-16 text-primary-100;
}

.loading-circle-bg {
  @apply opacity-25;
  fill: none;
  stroke: currentColor;
  stroke-width: 2;
}

.loading-circle-progress {
  @apply opacity-75;
  fill: none;
  stroke: currentColor;
  stroke-width: 2;
  stroke-linecap: round;
  animation: loading-progress 2s ease-in-out infinite;
  stroke-dasharray: 31.416;
  stroke-dashoffset: 31.416;
}

@keyframes loading-progress {
  0% {
    stroke-dashoffset: 31.416;
  }

  50% {
    stroke-dashoffset: 0;
  }

  100% {
    stroke-dashoffset: -31.416;
  }
}

.loading-text {
  @apply text-center space-y-2;
}

.loading-title {
  @apply text-xl font-bold text-gray-900 dark:text-white;
}

.loading-subtitle {
  @apply text-sm text-gray-600 dark:text-gray-400;
}

// ===== CHAPTER IMAGES =====
.chapter-images-container {
  @apply w-full;
}

.chapter-page-container {
  @apply block relative object-contain mx-auto;
}

.chapter-page-wrapper {
  @apply w-full h-auto;
}

.chapter-page-image {
  @apply object-cover w-full h-auto;

  &.chapter-page-horizontal {
    @apply min-w-80 h-full;
  }

  &.night-mode {
    filter: brightness(0.9) sepia(0.4);
  }
}

// ===== SKELETON LOADING =====
.skeleton {
  @apply bg-gray-200 dark:bg-neutral-700 rounded-lg animate-pulse w-full;
}

// ===== PERFORMANCE OPTIMIZATIONS =====
.chapter-page-container {
  // CSS containment for better performance
  contain: layout style paint;
  
  // Hardware acceleration
  will-change: transform;
  
  // Optimize rendering
  transform: translateZ(0);
}

.chapter-page-image {
  // Optimize image rendering
  image-rendering: -webkit-optimize-contrast;
  image-rendering: crisp-edges;
  
  // Prevent layout shifts
  max-width: 100%;
  height: auto;
}

// ===== RESPONSIVE DESIGN =====
@media (max-width: 1024px) {
  .reading-content {
    @apply mx-0 lg:mx-6;
  }
}

@media (max-width: 640px) {
  .scroll-btn-text {
    @apply hidden;
  }
  
  .scroll-btn {
    @apply px-3 py-2;
  }
}

@media (max-width: 480px) {
  .reading-container {
    @apply px-2;
  }
  
  .scroll-navigation {
    @apply px-2;
  }
}
